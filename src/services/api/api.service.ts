// import types from custom Moleculer typedef
import { BrokerNode, Context, Service, ServiceBroker } from '../../types/moleculer';
// import other services types
import { ApiServiceTypes, RequestMessage } from '../../types';
// use moleculer-decorators
import { Event, Method, Service as DService } from 'moleculer-decorators';
import ApiGateway from 'moleculer-web';
import { regenerateTargets } from '../../middlewares/prometheus-file-generator.middleware';
import { IncomingMessage, ServerResponse } from 'http';
import { CommonErrors } from '../../commons/error.helper';
import compression from 'compression';
import { PERMISSIONS } from '../../entities/user.entity';
import { initError } from '../../utils/ErrorTracking';
import { getRealIp } from '../../utils/IpUtils';

interface ApiService {
  name: typeof ApiServiceTypes.name;
}

@DService({
  name: ApiServiceTypes.name,
  // @ts-ignore
  mixins: [ApiGateway],
  // More info about settings: https://moleculer.services/docs/0.14/moleculer-web.html
  settings: {
    port: process.env.PORT || 3000,

    use: [compression()],

    cors: {
      methods: ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT'],
      origin: '*', // Allow all sources
    },

    routes: [
      {
        path: "/api/zalo/auth/login",
        aliases: {
          "GET /": "zalo.loginQR2"
        },

        // 🚫 Disable compression
        mappingPolicy: "restrict",
        compression: false
      },
      {
        path: '/api',
        whitelist: [
          // Access to any actions in all services under "/api" URL
          // '**',
          /^((?!\$node).)*\.\w+$/, // All node except $node.xxx
        ],
        // Route-level Express middlewares. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Middlewares
        use: [],
        // Enable/disable parameter merging method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Disable-merging
        mergeParams: true,

        // Enable authentication. Implement the logic into `authenticate` method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Authentication
        authentication: true,

        // Enable authorization. Implement the logic into `authorize` method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Authorization
        authorization: true,

        // The auto-alias feature allows you to declare your route alias directly in your services.
        // The gateway will dynamically build the full routes from service schema.
        autoAliases: true,

        aliases: {
          // File upload from HTML multipart form
          "POST /dataset/import": "multipart:dataset.actionImportKnowledge",
          "PUT /botSetting/updateLogo/:bot_id": "multipart:botSetting.updateLogo",
        },
        /**
         * Before call hook. You can check the request.
         * @param {Context} ctx
         * @param {Object} route
         * @param {IncomingMessage} req
         * @param {ServerResponse} res
         * @param {Object} data
         */
        onBeforeCall(
          ctx: Context<any, { userAgent: string; clientIp: string }>,
          route: object,
          req: IncomingMessage,
          res: ServerResponse,
        ) {
          //Set request headers to context meta
          ctx.meta.userAgent = req.headers['user-agent'];

          // Whitelist can be configured using the PROXY_IP_WHITELIST environment variable
          // Format: comma-separated list of IP addresses or CIDR ranges
          // Example: "***********,10.0.0.0/8,**********/12"
          ctx.meta.clientIp = getRealIp(req.socket.remoteAddress, req.headers, process.env.PROXY_IP_WHITELIST);
        },

        /**
         * After call hook. You can modify the data.
         * @param {Context} ctx
         * @param {Object} route
         * @param {IncomingMessage} req
         * @param {ServerResponse} res
         * @param {Object} data
         *
         onAfterCall(ctx: Context, route: object, req: IncomingMessage, res: ServerResponse, data: object) {
         // Async function which return with Promise
         return doSomething(ctx, res, data);
         },
         */

        // Calling options. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Calling-options
        callingOptions: {},

        bodyParsers: {
          json: {
            strict: false,
            limit: '1MB',
          },
          urlencoded: {
            extended: true,
            limit: '1MB',
          },
        },

        // Mapping policy setting. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Mapping-policy
        mappingPolicy: 'all', // Available values: "all", "restrict"

        // Enable/disable logging
        logging: true,
      },
      {
        path: '/api/monitor',
        whitelist: ['$node.*'],
        // Route-level Express middlewares. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Middlewares
        use: [
          function (req: any, res: any, next: any) {
            return this.hotPermissionMiddleware(['#dev.openapi:view'], req, res, next);
          },
        ],
        // Enable/disable parameter merging method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Disable-merging
        mergeParams: true,

        // The auto-alias feature allows you to declare your route alias directly in your services.
        // The gateway will dynamically build the full routes from service schema.
        autoAliases: true,

        aliases: {},

        // Mapping policy setting. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Mapping-policy
        mappingPolicy: 'all', // Available values: "all", "restrict"

        // Enable/disable logging
        logging: true,
      },
      // moleculer-auto-openapi routes
      {
        path: '/api/openapi',
        aliases: {
          'GET /openapi.json': [
            function (req: any, res: any, next: any) {
              // TODO: Add better way to block open api for unauthorized user
              //   return this.hotPermissionMiddleware(['#dev.openapi:view'], req, res, next);
              next();
            },
            'openapi.generateDocs', // swagger scheme
          ],
          'GET /ui': 'openapi.ui', // ui
        },
      },
    ],
    // Do not log client side errors (does not log an error response when the error.code is 400<=X<500)
    log4XXResponses: false,
    // Logging the request parameters. Set to any log level to enable it. E.g. "info"
    logRequestParams: null,
    // Logging the response data. Set to any log level to enable it. E.g. "info"
    logResponseData: null,
    // Serve assets from "public" folder
    assets: {
      folder: 'public',
      // Options to `server-static` module
      options: {},
    },
  },

  methods: {
    /**
     * Authenticate the request. It checks the `Authorization` token value in the request header.
     * Check the token value & resolve the user by the token.
     * The resolved user will be available in `ctx.meta.user`
     *
     * PLEASE NOTE, IT'S JUST AN EXAMPLE IMPLEMENTATION. DO NOT USE IN PRODUCTION!
     *
     * @param {Context} ctx
     * @param {any} route
     * @param {IncomingMessage} req
     * @returns {Promise}

     async authenticate (ctx: Context, route: any, req: IncomingMessage): Promise < any >  => {
     // Read the token from header
     const auth = req.headers.authorization;

     if (auth && auth.startsWith("Bearer")) {
     const token = auth.slice(7);

     // Check the token. Tip: call a service which verify the token. E.g. `accounts.resolveToken`
     if (token === "123456") {
     // Returns the resolved user. It will be set to the `ctx.meta.user`
     return {
     id: 1,
     name: "John Doe",
     };

     } else {
     // Invalid token
     throw new ApiGateway.Errors.UnAuthorizedError(ApiGateway.Errors.ERR_INVALID_TOKEN, {
     error: "Invalid Token",
     });
     }

     } else {
     // No token. Throw an error or do nothing if anonymous access is allowed.
     // Throw new E.UnAuthorizedError(E.ERR_NO_TOKEN);
     return null;
     }
     },
     */
    /**
     * Authorize the request. Check that the authenticated user has right to access the resource.
     *
     * PLEASE NOTE, IT'S JUST AN EXAMPLE IMPLEMENTATION. DO NOT USE IN PRODUCTION!
     *
     * @param {Context} ctx
     * @param {Object} route
     * @param {IncomingMessage} req
     * @returns {Promise}

     async authorize (ctx: Context < any, {
     user: string;
     } > , route: Record<string, undefined>, req: IncomingMessage): Promise < any > => {
     // Get the authenticated user.
     const user = ctx.meta.user;

     // It check the `auth` property in action schema.
     // @ts-ignore
     if (req.$action.auth === "required" && !user) {
     throw new ApiGateway.Errors.UnAuthorizedError("NO_RIGHTS", {
     error: "Unauthorized",
     });
     }
     },
     */
  },
})
class ApiService extends Service implements ApiServiceTypes.ServiceOwnActions {
  declare settings: any;
  // @Event({
  //   name: eventName('user.nodeChange'),
  // })
  // someEvent(payload: UserServiceTypes.EventParams<'nodeChange'>) {
  //   payload.id;
  //   payload.email;
  //   payload.password;
  //   payload.name;
  //
  //   this.broker.emit('user.nodeChange', payload);
  // }

  @Method
  async hotPermissionMiddleware(roles: (typeof PERMISSIONS)[number][], req: RequestMessage, res: any, next: any) {
    try {
      req.$ctx.meta.user = await this.authenticate(req.$ctx, req.$route, req, res, null, roles);
      await this.authorize(req.$ctx, req.$route, req, roles);
      next();
    } catch (e) {
      next(e);
    }
  }

  @Method
  async authenticate(
    ctx: Context<Record<string, unknown>, any>,
    route: any,
    req: RequestMessage,
    res: ServerResponse,
    alias: any,
    customRoles: string[] | number[],
  ) {
    // Read the token from header
    const auth = req.headers.authorization;
    const roles = customRoles ? customRoles : req.$action?.roles;

    if (auth && auth.startsWith('Bearer')) {
      const token = auth.slice(7)?.trim();
      ctx.meta.token = token;

      // Check the token. Tip: call a service which verify the token. E.g. `accounts.resolveToken`
      // Returns the resolved user. It will be set to the `ctx.meta.user`
      const user = await ctx.call('user.actionVerifyToken', { token });
      if (user) {
        return user;
      } else {
        // Invalid token
        throw new CommonErrors.UnAuthorizationError(ApiGateway.Errors.ERR_INVALID_TOKEN);
      }
    } else {
      // No token. Throw an error or do nothing if anonymous access is allowed.
      if (roles) {
        throw new CommonErrors.UnAuthorizationError(ApiGateway.Errors.ERR_NO_TOKEN);
      }
      return null;
    }
  }

  @Method
  async authorize(
    ctx: Context<Record<string, IncomingMessage>, any>,
    route: any,
    req: RequestMessage,
    customRoles: string[] | number[],
  ) {
    const user = ctx.meta.user;
    const roles = customRoles ? customRoles : req.$action?.roles;

    // It checks the `auth` property in action schema.
    if (
      // Logged user only. No need permissions
      (roles?.length === 0 && user) ||
      // Check special role
      (roles?.length > 0 &&
        (!user ||
          (roles.indexOf(user.role) === -1 &&
            roles.reduce(
              (previousValue: boolean, currentValue: string) =>
                previousValue && user.permissions?.indexOf(currentValue) === -1,
              true,
            ))))
    ) {
      throw new CommonErrors.ForbiddenError();
    }

    // // For some route, need inject token to child route
    // const listAllowToken = ['/user/logout', '/user/me'];
    // const requestURL = ctx.params.req?.url;
    // if (listAllowToken.includes(requestURL)) {
    //   const auth = ctx.params.req.headers.authorization;
    //   if (auth && auth.startsWith('Bearer')) {
    //     const token = auth.slice(7);
    //     ctx.meta.token = token;
    //   }
    // }
  }

  @Event({
    name: '$node.connected',
  })
  async nodeConnected(node: BrokerNode, hostname: string, eventName: string, context: Context) {
    // Update prometheus config on node update
    await regenerateTargets(this.broker, {});
  }

  @Event({
    name: '$node.disconnected',
  })
  async nodeDisconnected(node: BrokerNode, hostname: string, eventName: string, context: Context) {
    // Update prometheus config on node update
    await regenerateTargets(this.broker, {});
  }

  created() {
    // this.broker.call('user.create', { name: 'a' });
    this.waitForServices(['api', 'user']);
    console.log('===================API INFO ONLY FOR PRODUCTION=======================');
    console.log('Web: http://localhost:3000');
    console.log('Web admin: http://localhost:3001');
    console.log('Jaeger: http://localhost:3000/jaeger/');
    console.log('Prometheus: http://localhost:9090');
    console.log('Grafana: http://localhost:3005');
    console.log('===================API INFO ONLY FOR PRODUCTION=======================');

    initError(this.broker);
  }

  async started(broker: ServiceBroker) {
    // Update prometheus config on node update after second
    setTimeout(async () => {
      await regenerateTargets(this.broker, {});
    }, 1000);
  }
}

export = ApiService;
