<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Zalo Service Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    .status-online {
      background-color: #28a745;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
    }
    .status-offline {
      background-color: #dc3545;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
    }
    .account-card {
      border-left: 4px solid #007bff;
    }
    #qr-code img {
      max-width: 300px;
      max-height: 300px;
    }
    .loading {
      display: none;
    }
    .auth-required {
      display: none;
    }
  </style>
</head>
<body>
<div class="container mt-4">
  <h1 class="mb-4">
    <span id="status-indicator" class="status-offline me-2"></span>
    Zalo Service Dashboard
  </h1>

  <!-- Status Section -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h5>Authentication Status</h5>
        </div>
        <div class="card-body">
          <div id="status-info">
            <p id="status-text">Checking status...</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Login Section -->
  <div class="row mb-4" id="login-section">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5>Zalo Login</h5>
        </div>
        <div class="card-body text-center">
          <button id="login-btn" class="btn btn-primary mb-3">Login with QR Code</button>
          <div id="qr-code">
            <p class="text-muted">Click "Login with QR Code" to generate QR code</p>
          </div>
          <div id="login-status" class="mt-3"></div>
        </div>
      </div>
    </div>

    <!-- Accounts List -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5>Zalo Accounts</h5>
        </div>
        <div class="card-body">
          <div id="accounts-list">
            <p class="text-muted">No accounts authenticated</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message Sending Section -->
  <div class="row mb-4 auth-required" id="message-section">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5>Send Text Message</h5>
        </div>
        <div class="card-body">
          <form id="text-form">
            <div class="mb-3">
              <label for="text-recipient" class="form-label">Recipient (Phone/UserID)</label>
              <input type="text" class="form-control" id="text-recipient" placeholder="**********" required>
            </div>
            <div class="mb-3">
              <label for="text-message" class="form-label">Message</label>
              <textarea class="form-control" id="text-message" rows="3" placeholder="Enter your message" required></textarea>
            </div>
            <div class="mb-3">
              <label for="text-account" class="form-label">Account (optional)</label>
              <select class="form-control" id="text-account">
                <option value="">Use first available account</option>
              </select>
            </div>
            <button type="submit" class="btn btn-success">Send Message</button>
          </form>
          <div id="text-result" class="mt-3"></div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5>Send Group Message</h5>
        </div>
        <div class="card-body">
          <form id="group-form">
            <div class="mb-3">
              <label for="group-identifier" class="form-label">Group Name or ID</label>
              <input type="text" class="form-control" id="group-identifier" placeholder="Group name or ID" required>
            </div>
            <div class="mb-3">
              <label for="group-message" class="form-label">Message</label>
              <textarea class="form-control" id="group-message" rows="3" placeholder="Enter your message" required></textarea>
            </div>
            <div class="mb-3">
              <label for="group-account" class="form-label">Account (optional)</label>
              <select class="form-control" id="group-account">
                <option value="">Use first available account</option>
              </select>
            </div>
            <button type="submit" class="btn btn-success">Send to Group</button>
          </form>
          <div id="group-result" class="mt-3"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions Section -->
  <div class="row mb-4 auth-required" id="actions-section">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h5>Actions</h5>
        </div>
        <div class="card-body">
          <button id="refresh-status-btn" class="btn btn-info me-2">Refresh Status</button>
          <button id="logout-all-btn" class="btn btn-warning">Logout All Accounts</button>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
  // Global variables
  let eventSource = null;
  let currentAuthStatus = null;

  // DOM elements
  const statusIndicator = document.getElementById('status-indicator');
  const statusText = document.getElementById('status-text');
  const loginBtn = document.getElementById('login-btn');
  const qrCode = document.getElementById('qr-code');
  const loginStatus = document.getElementById('login-status');
  const accountsList = document.getElementById('accounts-list');
  const textForm = document.getElementById('text-form');
  const groupForm = document.getElementById('group-form');
  const textResult = document.getElementById('text-result');
  const groupResult = document.getElementById('group-result');
  const refreshStatusBtn = document.getElementById('refresh-status-btn');
  const logoutAllBtn = document.getElementById('logout-all-btn');
  const textAccountSelect = document.getElementById('text-account');
  const groupAccountSelect = document.getElementById('group-account');

  // Initialize the app
  checkAuthStatus();

  // Event listeners
  loginBtn.addEventListener('click', initiateLogin);
  refreshStatusBtn.addEventListener('click', checkAuthStatus);
  logoutAllBtn.addEventListener('click', logoutAll);
  textForm.addEventListener('submit', sendTextMessage);
  groupForm.addEventListener('submit', sendGroupMessage);

  // Check authentication status
  async function checkAuthStatus() {
    try {
      const response = await fetch('/api/zalo/auth/status');
      const data = await response.json();

      if (data.success) {
        currentAuthStatus = data.data;
        updateUI(data.data);
      } else {
        console.error('Error getting auth status:', data.message);
        showError('Failed to get authentication status');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      showError('Connection error while checking status');
    }
  }

  // Update UI based on auth status
  function updateUI(authStatus) {
    const isAuthenticated = authStatus.authenticatedAccounts > 0;

    // Update status indicator
    statusIndicator.className = isAuthenticated ? 'status-online me-2' : 'status-offline me-2';
    statusText.textContent = `${authStatus.authenticatedAccounts}/${authStatus.totalAccounts} accounts authenticated`;

    // Update accounts list
    updateAccountsList(authStatus.accounts);

    // Update account selects
    updateAccountSelects(authStatus.accounts);

    // Show/hide sections
    const authRequiredSections = document.querySelectorAll('.auth-required');
    authRequiredSections.forEach(section => {
      section.style.display = isAuthenticated ? 'block' : 'none';
    });
  }

  // Update accounts list
  function updateAccountsList(accounts) {
    const authenticatedAccounts = accounts.filter(acc => acc.isAuthenticated);

    if (authenticatedAccounts.length === 0) {
      accountsList.innerHTML = '<p class="text-muted">No accounts authenticated</p>';
      return;
    }

    accountsList.innerHTML = authenticatedAccounts.map(account => `
        <div class="card mb-2 account-card">
          <div class="card-body py-2">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <strong>${account.user?.name || 'Unknown'}</strong><br>
                <small class="text-muted">ID: ${account.user?.id || 'Unknown'}</small>
              </div>
              <button class="btn btn-sm btn-outline-danger" onclick="removeAccount('${account.id}', '${account.name}')">
                Remove
              </button>
            </div>
          </div>
        </div>
      `).join('');
  }

  // Update account select options
  function updateAccountSelects(accounts) {
    const authenticatedAccounts = accounts.filter(acc => acc.isAuthenticated);
    const options = authenticatedAccounts.map(account =>
      `<option value="${account.id}">${account.user?.name || account.name}</option>`
    ).join('');

    textAccountSelect.innerHTML = '<option value="">Use first available account</option>' + options;
    groupAccountSelect.innerHTML = '<option value="">Use first available account</option>' + options;
  }

  // Initiate login process
  function initiateLogin() {
    loginBtn.disabled = true;
    loginBtn.textContent = 'Connecting...';
    qrCode.innerHTML = '<p>Connecting to login service...</p>';
    loginStatus.innerHTML = '';

    // Close existing EventSource if any
    if (eventSource) {
      eventSource.close();
    }

    // Start SSE connection
    eventSource = new EventSource('/api/zalo/auth/login');

    eventSource.addEventListener('connected', function(event) {
      console.log('Connected to login service');
      qrCode.innerHTML = '<p>Generating QR code...</p>';
    });

    eventSource.addEventListener('qr', function(event) {
      const data = JSON.parse(event.data);
      console.log('QR code received');
      displayQRCode(data.data);
    });

    eventSource.addEventListener('qr_scanned', function(event) {
      const data = JSON.parse(event.data);
      console.log('QR code scanned');
      loginStatus.innerHTML = `<div class="alert alert-info">QR code scanned by: ${data.displayName}</div>`;
    });

    eventSource.addEventListener('auth_success', function(event) {
      const data = JSON.parse(event.data);
      console.log('Login successful');
      loginStatus.innerHTML = '<div class="alert alert-success">Login successful!</div>';
      qrCode.innerHTML = '<div class="text-success text-center"><i class="bi bi-check-circle-fill" style="font-size: 3rem;"></i><h5 class="mt-2">Login Successful!</h5></div>';

      eventSource.close();
      eventSource = null;

      // Refresh status after successful login
      setTimeout(checkAuthStatus, 1000);
    });

    eventSource.addEventListener('error', function(event) {
      const data = JSON.parse(event.data);
      console.error('Login error:', data);
      loginStatus.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
    });

    eventSource.onerror = function(error) {
      console.error('SSE connection error:', error);
      loginStatus.innerHTML = '<div class="alert alert-danger">Connection error</div>';
      eventSource = null;
    };

    // Re-enable login button
    loginBtn.disabled = false;
    loginBtn.textContent = 'Login with QR Code';
  }

  // Display QR code
  function displayQRCode(qrDataUrl) {
    const img = document.createElement('img');
    img.src = qrDataUrl.startsWith('data:image') ? qrDataUrl : 'data:image/png;base64,' + qrDataUrl;
    img.alt = 'Zalo Login QR Code';
    img.className = 'img-fluid';

    qrCode.innerHTML = '';
    qrCode.appendChild(img);
  }

  // Send text message
  async function sendTextMessage(event) {
    event.preventDefault();

    const recipient = document.getElementById('text-recipient').value;
    const text = document.getElementById('text-message').value;
    const accountId = document.getElementById('text-account').value;

    try {
      const body = new URLSearchParams();
      body.append('recipient', recipient);
      body.append('text', text);
      if (accountId) body.append('accountId', accountId);

      const response = await fetch('/api/zalo/messages/text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: body
      });

      const data = await response.json();

      if (data.success) {
        textResult.innerHTML = '<div class="alert alert-success">Message sent successfully!</div>';
        textForm.reset();
      } else {
        textResult.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
      }
    } catch (error) {
      textResult.innerHTML = `<div class="alert alert-danger">Connection error: ${error.message}</div>`;
    }
  }

  // Send group message
  async function sendGroupMessage(event) {
    event.preventDefault();

    const groupIdentifier = document.getElementById('group-identifier').value;
    const text = document.getElementById('group-message').value;
    const accountId = document.getElementById('group-account').value;

    try {
      const body = new URLSearchParams();
      body.append('groupIdentifier', groupIdentifier);
      body.append('text', text);
      if (accountId) body.append('accountId', accountId);

      const response = await fetch('/api/zalo/messages/group', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: body
      });

      const data = await response.json();

      if (data.success) {
        groupResult.innerHTML = '<div class="alert alert-success">Group message sent successfully!</div>';
        groupForm.reset();
      } else {
        groupResult.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
      }
    } catch (error) {
      groupResult.innerHTML = `<div class="alert alert-danger">Connection error: ${error.message}</div>`;
    }
  }

  // Remove account
  async function removeAccount(accountId, accountName) {
    if (!confirm(`Are you sure you want to remove account "${accountName}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/zalo/auth/accounts/${accountId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        showSuccess('Account removed successfully');
        checkAuthStatus();
      } else {
        showError(`Error removing account: ${data.message}`);
      }
    } catch (error) {
      showError(`Connection error: ${error.message}`);
    }
  }

  // Logout all accounts
  async function logoutAll() {
    if (!confirm('Are you sure you want to logout all accounts?')) {
      return;
    }

    try {
      const response = await fetch('/api/zalo/auth/logout-all', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        showSuccess('All accounts logged out successfully');
        checkAuthStatus();
      } else {
        showError(`Error logging out: ${data.message}`);
      }
    } catch (error) {
      showError(`Connection error: ${error.message}`);
    }
  }

  // Utility functions
  function showError(message) {
    console.error(message);
    // You can implement a toast or alert system here
  }

  function showSuccess(message) {
    console.log(message);
    // You can implement a toast or alert system here
  }

  // Make removeAccount available globally for onclick handlers
  window.removeAccount = removeAccount;
</script>
</body>
</html> 
